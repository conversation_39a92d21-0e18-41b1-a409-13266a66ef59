package com.gianghp.dried_tea_shop.mapper;

import com.gianghp.dried_tea_shop.dto.auth.SignUpRequestDto;
import com.gianghp.dried_tea_shop.dto.auth.SignUpResponseDto;
import com.gianghp.dried_tea_shop.dto.common.UserDto;
import com.gianghp.dried_tea_shop.dto.user.UpdateUserRequestDto;
import com.gianghp.dried_tea_shop.entity.User;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(componentModel = "spring", uses = {UuidMapper.class})
public interface UserMapper {

    @Mapping(source = "id", target = "userId", qualifiedByName = "uuidToString")
    @Mapping(source = "createdAt", target = "createdAt", dateFormat = "yyyy-MM-dd'T'HH:mm:ss")
    UserDto toUserDto(User user);

    @Mapping(source = "id", target = "userId", qualifiedByName = "uuidToString")
    @Mapping(source = "createdAt", target = "createdAt", dateFormat = "yyyy-MM-dd'T'HH:mm:ss")
    SignUpResponseDto toSignUpResponseDto(User user);
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "passwordHash", ignore = true)
    @Mapping(target = "role", ignore = true)
    @Mapping(target = "reviews", ignore = true)
    @Mapping(target = "cart", ignore = true)
    @Mapping(target = "orders", ignore = true)
    User toEntity(SignUpRequestDto signUpRequestDto);
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "email", ignore = true)
    @Mapping(target = "passwordHash", ignore = true)
    @Mapping(target = "reviews", ignore = true)
    @Mapping(target = "cart", ignore = true)
    @Mapping(target = "orders", ignore = true)
    void updateEntityFromDto(UpdateUserRequestDto updateUserRequestDto, @MappingTarget User user);
}
