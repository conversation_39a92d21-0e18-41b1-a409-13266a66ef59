package com.gianghp.dried_tea_shop.repository;

import com.gianghp.dried_tea_shop.entity.OrderItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Repository
public interface OrderItemRepository extends JpaRepository<OrderItem, UUID> {
    
    /**
     * Find order items by order ID
     */
    List<OrderItem> findByOrderId(UUID orderId);
    
    /**
     * Find order items by product ID
     */
    List<OrderItem> findByProductId(UUID productId);
    
    /**
     * Find order items by order and product
     */
    List<OrderItem> findByOrderIdAndProductId(UUID orderId, UUID productId);
    
    /**
     * Find order items by quantity
     */
    List<OrderItem> findByQuantity(Integer quantity);
    
    /**
     * Find order items by quantity range
     */
    List<OrderItem> findByQuantityBetween(Integer minQuantity, Integer maxQuantity);
    
    /**
     * Find order items by unit price range
     */
    List<OrderItem> findByUnitPriceBetween(BigDecimal minPrice, BigDecimal maxPrice);
    
    /**
     * Find order items by user ID
     */
    @Query("SELECT oi FROM OrderItem oi WHERE oi.order.user.id = :userId")
    List<OrderItem> findByUserId(@Param("userId") UUID userId);
    
    /**
     * Find order items by order status
     */
    @Query("SELECT oi FROM OrderItem oi WHERE oi.order.status = :status")
    List<OrderItem> findByOrderStatus(@Param("status") String status);
    
    /**
     * Find order items by product category
     */
    @Query("SELECT oi FROM OrderItem oi WHERE oi.product.category.id = :categoryId")
    List<OrderItem> findByProductCategoryId(@Param("categoryId") UUID categoryId);
    
    /**
     * Find order items by product name containing
     */
    @Query("SELECT oi FROM OrderItem oi WHERE LOWER(oi.product.name) LIKE LOWER(CONCAT('%', :productName, '%'))")
    List<OrderItem> findByProductNameContaining(@Param("productName") String productName);
    
    /**
     * Find order items by date range
     */
    @Query("SELECT oi FROM OrderItem oi WHERE oi.order.createdAt BETWEEN :startDate AND :endDate")
    List<OrderItem> findByDateRange(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);
    
    /**
     * Calculate total quantity sold for product
     */
    @Query("SELECT COALESCE(SUM(oi.quantity), 0) FROM OrderItem oi WHERE oi.product.id = :productId")
    long getTotalQuantitySoldByProductId(@Param("productId") UUID productId);
    
    /**
     * Calculate total revenue for product
     */
    @Query("SELECT COALESCE(SUM(oi.quantity * oi.unitPrice), 0) FROM OrderItem oi WHERE oi.product.id = :productId")
    BigDecimal getTotalRevenueByProductId(@Param("productId") UUID productId);
    
    /**
     * Calculate total quantity in order
     */
    @Query("SELECT COALESCE(SUM(oi.quantity), 0) FROM OrderItem oi WHERE oi.order.id = :orderId")
    long getTotalQuantityByOrderId(@Param("orderId") UUID orderId);
    
    /**
     * Find best selling products
     */
    @Query("SELECT oi.product.id, SUM(oi.quantity) as totalSold FROM OrderItem oi " +
           "WHERE oi.order.status = :orderStatus " +
           "GROUP BY oi.product.id ORDER BY totalSold DESC")
    List<Object[]> findBestSellingProducts(@Param("orderStatus") String orderStatus);
    
    /**
     * Find top revenue generating products
     */
    @Query("SELECT oi.product.id, SUM(oi.quantity * oi.unitPrice) as totalRevenue FROM OrderItem oi " +
           "WHERE oi.order.status = :orderStatus " +
           "GROUP BY oi.product.id ORDER BY totalRevenue DESC")
    List<Object[]> findTopRevenueProducts(@Param("orderStatus") String orderStatus);
    
    /**
     * Count order items by order ID
     */
    long countByOrderId(UUID orderId);
    
    /**
     * Count order items by product ID
     */
    long countByProductId(UUID productId);
    
    /**
     * Find order items with high quantity
     */
    List<OrderItem> findByQuantityGreaterThan(Integer quantity);
    
    /**
     * Find order items by user email
     */
    @Query("SELECT oi FROM OrderItem oi WHERE oi.order.user.email = :email")
    List<OrderItem> findByUserEmail(@Param("email") String email);
}
