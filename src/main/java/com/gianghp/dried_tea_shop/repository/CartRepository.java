package com.gianghp.dried_tea_shop.repository;

import com.gianghp.dried_tea_shop.entity.Cart;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface CartRepository extends JpaRepository<Cart, UUID> {
    
    /**
     * Find cart by user ID
     */
    Optional<Cart> findByUserId(UUID userId);
    
    /**
     * Check if cart exists for user
     */
    boolean existsByUserId(UUID userId);
    
    /**
     * Find carts with items
     */
    @Query("SELECT DISTINCT c FROM Cart c JOIN c.cartItems ci")
    List<Cart> findCartsWithItems();
    
    /**
     * Find empty carts
     */
    @Query("SELECT c FROM Cart c WHERE c.cartItems IS EMPTY")
    List<Cart> findEmptyCarts();
    
    /**
     * Count items in cart
     */
    @Query("SELECT COUNT(ci) FROM CartItem ci WHERE ci.cart.id = :cartId")
    long countItemsInCart(@Param("cartId") UUID cartId);
    
    /**
     * Calculate total quantity in cart
     */
    @Query("SELECT COALESCE(SUM(ci.quantity), 0) FROM CartItem ci WHERE ci.cart.id = :cartId")
    long getTotalQuantityInCart(@Param("cartId") UUID cartId);
    
    /**
     * Find carts by user email
     */
    @Query("SELECT c FROM Cart c WHERE c.user.email = :email")
    Optional<Cart> findByUserEmail(@Param("email") String email);
    
    /**
     * Find carts with specific product
     */
    @Query("SELECT DISTINCT c FROM Cart c JOIN c.cartItems ci WHERE ci.product.id = :productId")
    List<Cart> findCartsWithProduct(@Param("productId") UUID productId);
    
    /**
     * Find carts by user role
     */
    @Query("SELECT c FROM Cart c WHERE c.user.role = :role")
    List<Cart> findCartsByUserRole(@Param("role") String role);
}
