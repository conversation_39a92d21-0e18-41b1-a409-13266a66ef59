package com.gianghp.dried_tea_shop.repository;

import com.gianghp.dried_tea_shop.entity.Review;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface ReviewRepository extends JpaRepository<Review, UUID> {
    
    /**
     * Find reviews by product ID
     */
    List<Review> findByProductId(UUID productId);
    
    /**
     * Find reviews by product ID with pagination
     */
    Page<Review> findByProductId(UUID productId, Pageable pageable);
    
    /**
     * Find reviews by user ID
     */
    List<Review> findByUserId(UUID userId);
    
    /**
     * Find reviews by user ID with pagination
     */
    Page<Review> findByUserId(UUID userId, Pageable pageable);
    
    /**
     * Find review by user and product
     */
    Optional<Review> findByUserIdAndProductId(UUID userId, UUID productId);
    
    /**
     * Check if review exists for user and product
     */
    boolean existsByUserIdAndProductId(UUID userId, UUID productId);
    
    /**
     * Find reviews by rating
     */
    List<Review> findByRating(Integer rating);
    
    /**
     * Find reviews by rating range
     */
    List<Review> findByRatingBetween(Integer minRating, Integer maxRating);
    
    /**
     * Find reviews by rating greater than or equal
     */
    List<Review> findByRatingGreaterThanEqual(Integer rating);
    
    /**
     * Find reviews by rating less than or equal
     */
    List<Review> findByRatingLessThanEqual(Integer rating);
    
    /**
     * Find reviews with comments
     */
    @Query("SELECT r FROM Review r WHERE r.comment IS NOT NULL AND r.comment != ''")
    List<Review> findReviewsWithComments();
    
    /**
     * Find reviews without comments
     */
    @Query("SELECT r FROM Review r WHERE r.comment IS NULL OR r.comment = ''")
    List<Review> findReviewsWithoutComments();
    
    /**
     * Find reviews by comment containing (case insensitive)
     */
    @Query("SELECT r FROM Review r WHERE LOWER(r.comment) LIKE LOWER(CONCAT('%', :keyword, '%'))")
    List<Review> findByCommentContaining(@Param("keyword") String keyword);
    
    /**
     * Find reviews by date range
     */
    @Query("SELECT r FROM Review r WHERE r.createdAt BETWEEN :startDate AND :endDate")
    List<Review> findByDateRange(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);
    
    /**
     * Find recent reviews
     */
    @Query("SELECT r FROM Review r WHERE r.createdAt >= :since ORDER BY r.createdAt DESC")
    List<Review> findRecentReviews(@Param("since") LocalDateTime since);
    
    /**
     * Find reviews by product and rating
     */
    List<Review> findByProductIdAndRating(UUID productId, Integer rating);
    
    /**
     * Find reviews by product and rating range
     */
    List<Review> findByProductIdAndRatingBetween(UUID productId, Integer minRating, Integer maxRating);
    
    /**
     * Calculate average rating for product
     */
    @Query("SELECT AVG(r.rating) FROM Review r WHERE r.product.id = :productId")
    Double getAverageRatingByProductId(@Param("productId") UUID productId);
    
    /**
     * Count reviews by product
     */
    long countByProductId(UUID productId);
    
    /**
     * Count reviews by user
     */
    long countByUserId(UUID userId);
    
    /**
     * Count reviews by rating
     */
    long countByRating(Integer rating);
    
    /**
     * Count reviews by product and rating
     */
    long countByProductIdAndRating(UUID productId, Integer rating);
    
    /**
     * Find reviews by user email
     */
    @Query("SELECT r FROM Review r WHERE r.user.email = :email")
    List<Review> findByUserEmail(@Param("email") String email);
    
    /**
     * Find reviews by product name containing
     */
    @Query("SELECT r FROM Review r WHERE LOWER(r.product.name) LIKE LOWER(CONCAT('%', :productName, '%'))")
    List<Review> findByProductNameContaining(@Param("productName") String productName);
    
    /**
     * Find reviews by product category
     */
    @Query("SELECT r FROM Review r WHERE r.product.category.id = :categoryId")
    List<Review> findByProductCategoryId(@Param("categoryId") UUID categoryId);
    
    /**
     * Find top rated reviews for product
     */
    @Query("SELECT r FROM Review r WHERE r.product.id = :productId ORDER BY r.rating DESC, r.createdAt DESC")
    List<Review> findTopRatedReviewsByProductId(@Param("productId") UUID productId, Pageable pageable);
    
    /**
     * Find latest reviews for product
     */
    @Query("SELECT r FROM Review r WHERE r.product.id = :productId ORDER BY r.createdAt DESC")
    List<Review> findLatestReviewsByProductId(@Param("productId") UUID productId, Pageable pageable);
}
