package com.gianghp.dried_tea_shop.repository;

import com.gianghp.dried_tea_shop.entity.Category;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface CategoryRepository extends JpaRepository<Category, UUID> {
    
    /**
     * Find category by name
     */
    Optional<Category> findByName(String name);
    
    /**
     * Check if name exists
     */
    boolean existsByName(String name);
    
    /**
     * Find categories by name containing (case insensitive)
     */
    List<Category> findByNameContainingIgnoreCase(String name);
    
    /**
     * Find categories by description containing (case insensitive)
     */
    List<Category> findByDescriptionContainingIgnoreCase(String description);
    
    /**
     * Find categories with products
     */
    @Query("SELECT DISTINCT c FROM Category c JOIN c.products p")
    List<Category> findCategoriesWithProducts();
    
    /**
     * Find categories without products
     */
    @Query("SELECT c FROM Category c WHERE c.products IS EMPTY")
    List<Category> findCategoriesWithoutProducts();
    
    /**
     * Count products in category
     */
    @Query("SELECT COUNT(p) FROM Product p WHERE p.category.id = :categoryId")
    long countProductsInCategory(@Param("categoryId") UUID categoryId);
    
    /**
     * Find categories ordered by product count
     */
    @Query("SELECT c FROM Category c LEFT JOIN c.products p GROUP BY c ORDER BY COUNT(p) DESC")
    List<Category> findCategoriesOrderedByProductCount();
    
    /**
     * Find categories by name or description containing
     */
    @Query("SELECT c FROM Category c WHERE " +
           "LOWER(c.name) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(c.description) LIKE LOWER(CONCAT('%', :keyword, '%'))")
    List<Category> findByNameOrDescriptionContaining(@Param("keyword") String keyword);
}
