package com.gianghp.dried_tea_shop.repository;

import com.gianghp.dried_tea_shop.entity.Image;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface ImageRepository extends JpaRepository<Image, UUID> {
    
    /**
     * Find image by name
     */
    Optional<Image> findByName(String name);
    
    /**
     * Find image by URL
     */
    Optional<Image> findByUrl(String url);
    
    /**
     * Check if name exists
     */
    boolean existsByName(String name);
    
    /**
     * Check if URL exists
     */
    boolean existsByUrl(String url);
    
    /**
     * Find images by name containing (case insensitive)
     */
    List<Image> findByNameContainingIgnoreCase(String name);
    
    /**
     * Find images with associated products
     */
    @Query("SELECT i FROM Image i WHERE i.product IS NOT NULL")
    List<Image> findImagesWithProducts();
    
    /**
     * Find images without associated products
     */
    @Query("SELECT i FROM Image i WHERE i.product IS NULL")
    List<Image> findImagesWithoutProducts();
    
    /**
     * Find images by URL pattern
     */
    @Query("SELECT i FROM Image i WHERE i.url LIKE %:pattern%")
    List<Image> findByUrlPattern(@Param("pattern") String pattern);
    
    /**
     * Find images by file extension
     */
    @Query("SELECT i FROM Image i WHERE i.url LIKE %:extension")
    List<Image> findByFileExtension(@Param("extension") String extension);
}
