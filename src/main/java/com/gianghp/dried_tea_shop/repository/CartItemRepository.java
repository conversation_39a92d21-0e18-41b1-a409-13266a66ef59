package com.gianghp.dried_tea_shop.repository;

import com.gianghp.dried_tea_shop.entity.CartItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface CartItemRepository extends JpaRepository<CartItem, UUID> {
    
    /**
     * Find cart items by cart ID
     */
    List<CartItem> findByCartId(UUID cartId);
    
    /**
     * Find cart items by product ID
     */
    List<CartItem> findByProductId(UUID productId);
    
    /**
     * Find cart item by cart and product
     */
    Optional<CartItem> findByCartIdAndProductId(UUID cartId, UUID productId);
    
    /**
     * Check if cart item exists for cart and product
     */
    boolean existsByCartIdAndProductId(UUID cartId, UUID productId);
    
    /**
     * Find cart items by user ID
     */
    @Query("SELECT ci FROM CartItem ci WHERE ci.cart.user.id = :userId")
    List<CartItem> findByUserId(@Param("userId") UUID userId);
    
    /**
     * Find cart items by quantity greater than
     */
    List<CartItem> findByQuantityGreaterThan(Integer quantity);
    
    /**
     * Find cart items by quantity range
     */
    List<CartItem> findByQuantityBetween(Integer minQuantity, Integer maxQuantity);
    
    /**
     * Calculate total quantity for user
     */
    @Query("SELECT COALESCE(SUM(ci.quantity), 0) FROM CartItem ci WHERE ci.cart.user.id = :userId")
    long getTotalQuantityByUserId(@Param("userId") UUID userId);
    
    /**
     * Calculate total quantity for product across all carts
     */
    @Query("SELECT COALESCE(SUM(ci.quantity), 0) FROM CartItem ci WHERE ci.product.id = :productId")
    long getTotalQuantityByProductId(@Param("productId") UUID productId);
    
    /**
     * Find cart items by product category
     */
    @Query("SELECT ci FROM CartItem ci WHERE ci.product.category.id = :categoryId")
    List<CartItem> findByProductCategoryId(@Param("categoryId") UUID categoryId);
    
    /**
     * Find cart items by product name containing
     */
    @Query("SELECT ci FROM CartItem ci WHERE LOWER(ci.product.name) LIKE LOWER(CONCAT('%', :productName, '%'))")
    List<CartItem> findByProductNameContaining(@Param("productName") String productName);
    
    /**
     * Find cart items with product status
     */
    @Query("SELECT ci FROM CartItem ci WHERE ci.product.status = :status")
    List<CartItem> findByProductStatus(@Param("status") String status);
    
    /**
     * Delete cart items by cart ID
     */
    void deleteByCartId(UUID cartId);
    
    /**
     * Delete cart items by product ID
     */
    void deleteByProductId(UUID productId);
    
    /**
     * Count cart items by cart ID
     */
    long countByCartId(UUID cartId);
    
    /**
     * Count cart items by product ID
     */
    long countByProductId(UUID productId);
}
