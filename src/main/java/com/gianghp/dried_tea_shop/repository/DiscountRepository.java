package com.gianghp.dried_tea_shop.repository;

import com.gianghp.dried_tea_shop.entity.Discount;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface DiscountRepository extends JpaRepository<Discount, UUID> {
    
    /**
     * Find discount by name
     */
    Optional<Discount> findByName(String name);
    
    /**
     * Check if name exists
     */
    boolean existsByName(String name);
    
    /**
     * Find discounts by active status
     */
    List<Discount> findByActive(Boolean active);
    
    /**
     * Find discounts by discount type
     */
    List<Discount> findByDiscountType(String discountType);
    
    /**
     * Find active discounts by type
     */
    List<Discount> findByActiveAndDiscountType(Boolean active, String discountType);
    
    /**
     * Find discounts by name containing (case insensitive)
     */
    List<Discount> findByNameContainingIgnoreCase(String name);
    
    /**
     * Find discounts by value range
     */
    List<Discount> findByValueBetween(BigDecimal minValue, BigDecimal maxValue);
    
    /**
     * Find current active discounts
     */
    @Query("SELECT d FROM Discount d WHERE d.active = true AND d.startDate <= :currentDate AND d.endDate >= :currentDate")
    List<Discount> findCurrentActiveDiscounts(@Param("currentDate") LocalDate currentDate);
    
    /**
     * Find expired discounts
     */
    @Query("SELECT d FROM Discount d WHERE d.endDate < :currentDate")
    List<Discount> findExpiredDiscounts(@Param("currentDate") LocalDate currentDate);
    
    /**
     * Find upcoming discounts
     */
    @Query("SELECT d FROM Discount d WHERE d.startDate > :currentDate AND d.active = true")
    List<Discount> findUpcomingDiscounts(@Param("currentDate") LocalDate currentDate);
    
    /**
     * Find discounts ending soon
     */
    @Query("SELECT d FROM Discount d WHERE d.active = true AND d.endDate BETWEEN :currentDate AND :endDate")
    List<Discount> findDiscountsEndingSoon(@Param("currentDate") LocalDate currentDate, @Param("endDate") LocalDate endDate);
    
    /**
     * Find discounts with products
     */
    @Query("SELECT DISTINCT d FROM Discount d JOIN d.products p")
    List<Discount> findDiscountsWithProducts();
    
    /**
     * Find discounts without products
     */
    @Query("SELECT d FROM Discount d WHERE d.products IS EMPTY")
    List<Discount> findDiscountsWithoutProducts();
    
    /**
     * Count products using discount
     */
    @Query("SELECT COUNT(p) FROM Product p WHERE p.discount.id = :discountId")
    long countProductsUsingDiscount(@Param("discountId") UUID discountId);
    
    /**
     * Find discounts by date range
     */
    @Query("SELECT d FROM Discount d WHERE " +
           "(d.startDate <= :endDate AND d.endDate >= :startDate)")
    List<Discount> findDiscountsByDateRange(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);
}
