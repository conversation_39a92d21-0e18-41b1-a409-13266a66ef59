package com.gianghp.dried_tea_shop.repository;

import com.gianghp.dried_tea_shop.entity.User;
import com.gianghp.dried_tea_shop.enums.UserRole;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface UserRepository extends JpaRepository<User, UUID> {
    
    /**
     * Find user by email
     */
    Optional<User> findByEmail(String email);
    
    /**
     * Check if email exists
     */
    boolean existsByEmail(String email);
    
    /**
     * Find users by role
     */
    List<User> findByRole(UserRole role);
    
    /**
     * Find users by name containing (case insensitive)
     */
    List<User> findByNameContainingIgnoreCase(String name);
    
    /**
     * Find users by phone
     */
    Optional<User> findByPhone(String phone);
    
    /**
     * Check if phone exists
     */
    boolean existsByPhone(String phone);
    
    /**
     * Find users by role and name containing
     */
    List<User> findByRoleAndNameContainingIgnoreCase(UserRole role, String name);
    
    /**
     * Count users by role
     */
    long countByRole(UserRole role);
    
    /**
     * Find users with orders
     */
    @Query("SELECT DISTINCT u FROM User u JOIN u.orders o")
    List<User> findUsersWithOrders();
    
    /**
     * Find users with reviews
     */
    @Query("SELECT DISTINCT u FROM User u JOIN u.reviews r")
    List<User> findUsersWithReviews();
    
    /**
     * Find users by email domain
     */
    @Query("SELECT u FROM User u WHERE u.email LIKE %:domain%")
    List<User> findByEmailDomain(@Param("domain") String domain);
}
