spring.application.name=dried-tea-shop
# Port ch?y ?ng d?ng
server.port=8888

# Encoding (UTF-8 cho request/response)
server.servlet.encoding.charset=UTF-8
server.servlet.encoding.enabled=true
server.servlet.encoding.force=true

# Hi?n th? l?i chi ti?t cho dev (?n khi production)
server.error.include-message=always
server.error.include-binding-errors=always


spring.datasource.url=***********************************************************************************************************************************************************************
spring.datasource.driver-class-name=org.postgresql.Driver

spring.jpa.hibernate.ddl-auto=update    
spring.jpa.show-sql=true                 
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect



# M?c log m?c ??nh
logging.level.root=INFO

# Log chi ti?t SQL
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE

# Log custom package
logging.level.com.gianghp.dried_tea_shop=DEBUG


# Hi?n th? thông ?i?p l?i validation trong response
spring.mvc.throw-exception-if-no-handler-found=true
spring.web.resources.add-mappings=false


springdoc.api-docs.enabled=true
springdoc.swagger-ui.enabled=true
springdoc.swagger-ui.path=/swagger-ui.html
#springdoc.api-docs.path=/api-docs

